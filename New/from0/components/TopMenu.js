import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Search,
  Bell,
  HelpCircle,
  Settings,
  Menu,
  User,
  LogOut,
  ChevronDown,
  FileText,
  Server,
  Users,
  Network,
  X,
  Layers,
  Box,
  XCircle,
  CheckCircle,
  Clock,
  AlertCircle,
  Activity,
  Pause,
  Play,
  RefreshCw,
  Zap,
  HardDrive,
  Download
} from 'lucide-react';
import { useAuth } from '../AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { API_URL } from '../config';
import InvoiceView from './InvoiceView';
const TopMenu = ({ toggleSidebar = () => {} }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [menuJustOpened, setMenuJustOpened] = useState(false);

  // Queue monitoring state
  const [showQueueMonitor, setShowQueueMonitor] = useState(false);
  const [queueData, setQueueData] = useState(null);
  const [queueLoading, setQueueLoading] = useState(false);
  const [queueMenuJustOpened, setQueueMenuJustOpened] = useState(false);
  const [manualAction, setManualAction] = useState(false);
  const [restartLoading, setRestartLoading] = useState(false);
  const [switchMapping, setSwitchMapping] = useState({});
  const [retryingTasks, setRetryingTasks] = useState(new Set());

  // PXE Installation monitoring state
  const [showPXEMonitor, setShowPXEMonitor] = useState(false);
  const [pxeData, setPXEData] = useState(null);
  const [pxeLoading, setPXELoading] = useState(false);
  const [pxeMenuJustOpened, setPXEMenuJustOpened] = useState(false);
  const [pxeManualAction, setPXEManualAction] = useState(false);
  const [retryingSessions, setRetryingSessions] = useState(new Set());
  const [cancellingSessions, setCancellingSessions] = useState(new Set());

  // Ticket notifications state
  const [showTicketNotifications, setShowTicketNotifications] = useState(false);
  const [ticketNotifications, setTicketNotifications] = useState(null);
  const [ticketNotificationsLoading, setTicketNotificationsLoading] = useState(false);
  const [ticketNotificationsMenuJustOpened, setTicketNotificationsMenuJustOpened] = useState(false);
  const [ticketNotificationsManualAction, setTicketNotificationsManualAction] = useState(false);

  // Invoice modal state
  const [invoiceModalOpen, setInvoiceModalOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);

  // Close invoice modal
  const closeInvoiceModal = useCallback(() => {
    setInvoiceModalOpen(false);
    setTimeout(() => {
      setSelectedInvoice(null);
    }, 100);
  }, []);

  // Add effect to prevent body scrolling when modal is open
  useEffect(() => {
    if (invoiceModalOpen) {
      // Disable scrolling on body when modal is open
      document.body.style.overflow = 'hidden';

      // Add a class to the sidebar to make it unclickable
      const sidebar = document.querySelector('.sidebar-container');
      if (sidebar) {
        sidebar.style.pointerEvents = 'none';
      }

      // Add event listener for ESC key to close modal
      const handleEscKey = (e) => {
        if (e.key === 'Escape') {
          closeInvoiceModal();
        }
      };
      document.addEventListener('keydown', handleEscKey);

      // Cleanup function for ESC key handler
      return () => {
        document.removeEventListener('keydown', handleEscKey);
        document.body.style.overflow = '';
        const sidebar = document.querySelector('.sidebar-container');
        if (sidebar) {
          sidebar.style.pointerEvents = '';
        }
      };
    } else {
      // Re-enable scrolling when modal is closed
      document.body.style.overflow = '';

      // Remove the class from the sidebar
      const sidebar = document.querySelector('.sidebar-container');
      if (sidebar) {
        sidebar.style.pointerEvents = '';
      }

      // No cleanup needed when modal is not open
      return () => {};
    }
  }, [invoiceModalOpen, closeInvoiceModal]);
  const searchRef = useRef(null);
  const userMenuRef = useRef(null);
  const queueMenuRef = useRef(null);
  const pxeMenuRef = useRef(null);
  const ticketNotificationsMenuRef = useRef(null);
  const { logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Reset search and close user menu on route changes
  useEffect(() => {
    setSearchQuery('');
    setSearchResults(null);
    setShowSearchResults(false);
    setShowUserMenu(false);
    setShowQueueMonitor(false);
    setShowPXEMonitor(false);
    setShowTicketNotifications(false);
  }, [location.pathname]);

  // Fetch switch mapping for queue monitor
  const fetchSwitchMapping = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=get_switches`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          const mapping = {};
          data.data.forEach(switchItem => {
            if (switchItem.switch_ip && switchItem.label) {
              mapping[switchItem.switch_ip] = switchItem.label;
            }
          });
          setSwitchMapping(mapping);
        }
      }
    } catch (error) {
      console.error('Error fetching switch mapping:', error);
    }
  }, []);

  // Helper function to get switch name by IP
  const getSwitchName = (switchIp) => {
    return switchMapping[switchIp] || switchIp; // Fallback to IP if name not found
  };

  // Fetch queue data
  const fetchQueueData = useCallback(async (isManual = false) => {
    // For manual calls, don't proceed if already loading
    // For automatic calls, proceed regardless to ensure data freshness
    if (isManual && queueLoading) return;
    
    if (isManual) {
      setManualAction(true);
      setQueueLoading(true);
    }
    
    try {
      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=get_switch_queue_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setQueueData(data);
        } else {
          console.error('Queue data fetch failed:', data.error);
        }
      }
    } catch (error) {
      console.error('Error fetching queue data:', error);
    } finally {
      if (isManual) {
        setQueueLoading(false);
        setManualAction(false);
      }
    }
  }, [queueLoading]);

  // Auto-refresh queue data when menu is open
  useEffect(() => {
    let interval;
    if (showQueueMonitor) {
      // Fetch switch mapping first if not already loaded
      if (Object.keys(switchMapping).length === 0) {
        fetchSwitchMapping();
      }
      // Fetch immediately when opened (manual)
      fetchQueueData(true);
      // Then refresh every 5 seconds (automatic, no loading indicator)
      interval = setInterval(() => fetchQueueData(false), 5000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [showQueueMonitor, fetchQueueData, switchMapping, fetchSwitchMapping]);

  // Handle queue menu toggle
  const toggleQueueMonitor = (e) => {
    e.preventDefault();
    e.stopPropagation();

    setQueueMenuJustOpened(true);
    setShowQueueMonitor(prev => !prev);

    setTimeout(() => {
      setQueueMenuJustOpened(false);
    }, 100);
  };

  // Handle queue restart
  const restartQueue = async () => {
    setRestartLoading(true);
    
    try {
      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=restart_switch_queue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Let the auto-refresh interval handle the data refresh
          // No manual refresh needed to avoid loading state conflicts
          console.log('Queue restart successful');
        }
      }
    } catch (error) {
      console.error('Error restarting queue:', error);
    } finally {
      setRestartLoading(false);
    }
  };

  // Handle retry failed task
  const retryTask = async (taskId) => {
    setRetryingTasks(prev => new Set([...prev, taskId]));
    
    try {
      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=retry_switch_queue_task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          task_id: taskId
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Refresh queue data to show updated status
          fetchQueueData(false);
          console.log('Task retry successful');
        } else {
          console.error('Task retry failed:', data.error);
        }
      }
    } catch (error) {
      console.error('Error retrying task:', error);
    } finally {
      setRetryingTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
    }
  };

  // Fetch ticket notifications data
  const fetchTicketNotifications = useCallback(async (isManual = false) => {
    // For manual calls, don't proceed if already loading
    // For automatic calls, proceed regardless to ensure data freshness
    if (isManual && ticketNotificationsLoading) return;

    if (isManual) {
      setTicketNotificationsManualAction(true);
      setTicketNotificationsLoading(true);
    }

    try {
      const response = await fetch(`${API_URL}/api_tickets_admin.php?f=get_ticket_notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTicketNotifications(data);
        } else {
          console.error('Ticket notifications fetch failed:', data.error);
        }
      }
    } catch (error) {
      console.error('Error fetching ticket notifications:', error);
    } finally {
      if (isManual) {
        setTicketNotificationsLoading(false);
        setTicketNotificationsManualAction(false);
      }
    }
  }, [ticketNotificationsLoading]);

  // Fetch PXE installation data
  const fetchPXEData = useCallback(async (isManual = false) => {
    // For manual calls, don't proceed if already loading
    // For automatic calls, proceed regardless to ensure data freshness
    if (isManual && pxeLoading) return;

    if (isManual) {
      setPXEManualAction(true);
      setPXELoading(true);
    }

    try {
      const response = await fetch(`${API_URL}/pxe_api_integration.php?f=get_pxe_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setPXEData(data);
        } else {
          console.error('PXE data fetch failed:', data.error);
        }
      }
    } catch (error) {
      console.error('Error fetching PXE data:', error);
    } finally {
      if (isManual) {
        setPXELoading(false);
        setPXEManualAction(false);
      }
    }
  }, [pxeLoading]);

  // Auto-refresh ticket notifications when menu is open
  useEffect(() => {
    let interval;
    if (showTicketNotifications) {
      // Fetch immediately when opened (manual)
      fetchTicketNotifications(true);
      // Then refresh every 30 seconds (automatic, no loading indicator)
      interval = setInterval(() => fetchTicketNotifications(false), 30000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [showTicketNotifications, fetchTicketNotifications]);

  // Auto-refresh PXE data when menu is open
  useEffect(() => {
    let interval;
    if (showPXEMonitor) {
      // Fetch immediately when opened (manual)
      fetchPXEData(true);
      // Then refresh every 5 seconds (automatic, no loading indicator)
      interval = setInterval(() => fetchPXEData(false), 5000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [showPXEMonitor, fetchPXEData]);

  // Background refresh for ticket notifications (runs continuously)
  useEffect(() => {
    // Fetch immediately on mount
    fetchTicketNotifications(false);

    // Then refresh every 2 minutes in background
    const interval = setInterval(() => fetchTicketNotifications(false), 120000);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [fetchTicketNotifications]);

  // Handle ticket notifications menu toggle
  const toggleTicketNotifications = (e) => {
    e.preventDefault();
    e.stopPropagation();

    setTicketNotificationsMenuJustOpened(true);
    setShowTicketNotifications(prev => !prev);

    setTimeout(() => {
      setTicketNotificationsMenuJustOpened(false);
    }, 100);
  };

  // Handle PXE menu toggle
  const togglePXEMonitor = (e) => {
    e.preventDefault();
    e.stopPropagation();

    setPXEMenuJustOpened(true);
    setShowPXEMonitor(prev => !prev);

    setTimeout(() => {
      setPXEMenuJustOpened(false);
    }, 100);
  };

  // Handle retry failed PXE session
  const retryPXESession = async (sessionId) => {
    setRetryingSessions(prev => new Set([...prev, sessionId]));
    
    try {
      const response = await fetch(`${API_URL}/pxe_api_integration.php?f=retry_pxe_session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          session_id: sessionId
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Refresh PXE data to show updated status
          fetchPXEData(false);
          console.log('PXE session retry successful');
        } else {
          console.error('PXE session retry failed:', data.error);
        }
      }
    } catch (error) {
      console.error('Error retrying PXE session:', error);
    } finally {
      setRetryingSessions(prev => {
        const newSet = new Set(prev);
        newSet.delete(sessionId);
        return newSet;
      });
    }
  };

  // Handle cancel PXE session
  const cancelPXESession = async (sessionId) => {
    // Show confirmation dialog
    const confirmCancel = window.confirm(
      'Are you sure you want to cancel this installation?\n\n' +
      'This will:\n' +
      '• Stop the PXE installation process\n' +
      '• Clean up installation files and DHCP entries\n' +
      '• Restart the server with normal boot order\n' +
      '• Mark the session as cancelled\n\n' +
      'This action cannot be undone.'
    );
    
    if (!confirmCancel) {
      return;
    }
    
    setCancellingSessions(prev => new Set([...prev, sessionId]));
    
    try {
      const response = await fetch(`${API_URL}/pxe_api_integration.php?f=cancel_pxe_session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          session_id: sessionId
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Refresh PXE data to show updated status
          fetchPXEData(false);
          console.log('PXE session cancelled successfully');
          
          // Show detailed cleanup information if available
          if (data.cleanup_details && data.cleanup_details.length > 0) {
            const cleanupInfo = data.cleanup_details.join('\n• ');
            alert(`Installation cancelled successfully!\n\nCleanup performed:\n• ${cleanupInfo}`);
          }
        } else {
          console.error('PXE session cancellation failed:', data.error);
          alert(`Failed to cancel installation: ${data.error}`);
        }
      } else {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Error cancelling PXE session:', error);
      alert(`Error cancelling installation: ${error.message}`);
    } finally {
      setCancellingSessions(prev => {
        const newSet = new Set(prev);
        newSet.delete(sessionId);
        return newSet;
      });
    }
  };

  // Handle user menu toggle
  const toggleUserMenu = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Set flag to prevent immediate closing
    setMenuJustOpened(true);

    // Toggle menu
    setShowUserMenu(prev => !prev);

    // Reset flag after a short delay
    setTimeout(() => {
      setMenuJustOpened(false);
    }, 100);
  };

  // Handle user menu clicks outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Only close if menu is open, click is outside menu, and menu wasn't just opened
      if (
        showUserMenu &&
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target) &&
        !menuJustOpened
      ) {
        setShowUserMenu(false);
      }
    };

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu, menuJustOpened]);

  // Handle queue menu clicks outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        showQueueMonitor &&
        queueMenuRef.current &&
        !queueMenuRef.current.contains(event.target) &&
        !queueMenuJustOpened
      ) {
        setShowQueueMonitor(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showQueueMonitor, queueMenuJustOpened]);

  // Handle ticket notifications menu clicks outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        showTicketNotifications &&
        ticketNotificationsMenuRef.current &&
        !ticketNotificationsMenuRef.current.contains(event.target) &&
        !ticketNotificationsMenuJustOpened
      ) {
        setShowTicketNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTicketNotifications, ticketNotificationsMenuJustOpened]);

  // Handle PXE menu clicks outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        showPXEMonitor &&
        pxeMenuRef.current &&
        !pxeMenuRef.current.contains(event.target) &&
        !pxeMenuJustOpened
      ) {
        setShowPXEMonitor(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPXEMonitor, pxeMenuJustOpened]);

  // Handle navigation to profile page
  const navigateToProfile = () => {
    navigate('/admin/account');
    setShowUserMenu(false);
  };

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/admin/login');
  };

  // Add click outside listener to close search results
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle search input change
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    if (query.length >= 2) {
      performSearch(query);
    } else {
      setSearchResults(null);
      setShowSearchResults(false);
    }
  };

  // Perform global search
  const performSearch = async (query) => {
    if (query.length < 2) return;

    setIsSearching(true);
    setShowSearchResults(true);

    try {
      const response = await fetch(`${API_URL}/api_admin_search.php?f=global_search&q=${encodeURIComponent(query)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setSearchResults(data.results);
      } else {
        setSearchResults(null);
      }
    } catch (err) {
      console.error("Error performing global search:", err);
      setSearchResults(null);
    } finally {
      setIsSearching(false);
    }
  };

  // Open invoice modal
  const openInvoiceModal = (invoice) => {
    console.log("Opening invoice modal for:", invoice);

    // Format the invoice data for the InvoiceView component
    const formattedInvoice = {
      id: invoice.id,
      isProforma: invoice.isProforma,
      client: invoice.client_name,
      clientEmail: invoice.client_email,
      clientId: invoice.client_id || invoice.original_id,
      date: invoice.date,
      dueDate: invoice.due_date,
      status: invoice.status,
      paymentMethod: invoice.payment_method || 'Bank Transfer',
      amount: `€${invoice.total}`,
      total: `€${invoice.total}`,
      subtotal: invoice.subtotal ? `€${invoice.subtotal}` : `€${invoice.total}`,
      tax: invoice.tax ? `€${invoice.tax}` : '€0.00',
      description: invoice.description || '', // Store the invoice description separately
      notes: invoice.description || '', // Also store as notes for compatibility
      // Use the items from the search results if available
      items: invoice.items || [{
        description: `${invoice.isProforma ? 'Proforma' : 'Invoice'} #${invoice.id}`,
        quantity: 1,
        unitPrice: `€${invoice.total}`,
        total: `€${invoice.total}`
      }]
    };

    setSelectedInvoice(formattedInvoice);
    setInvoiceModalOpen(true);
  };

  // Navigate to search result
  const navigateToResult = (type, id) => {
    setShowSearchResults(false);
    setSearchQuery('');

    switch (type) {
      case 'invoice':
        // Find the invoice in search results
        const invoice = searchResults.invoices.find(inv => inv.id === id);
        if (invoice) {
          openInvoiceModal(invoice);
        } else {
          console.error("Invoice not found in search results:", id);
        }
        break;
      case 'client':
        // Navigate to accounts page and open the client detail view
        // Store the ID first, then navigate
        localStorage.setItem('open_account_id', id);
        console.log("Set open_account_id in localStorage:", id);

        // Force a page reload to ensure the modal opens correctly
        if (location.pathname === '/admin/accounts') {
          // If already on the accounts page, reload the page
          window.location.href = '/admin/accounts';
        } else {
          // Otherwise use normal navigation
          navigate(`/admin/accounts`);
        }
        break;
      case 'server':
        navigate(`/admin/inventory/dedicated/${id}`);
        break;
      case 'switch':
        navigate(`/admin/inventory/switch/${id}`);
        break;
      case 'blade':
        navigate(`/admin/inventory/blade/${id}`);
        break;
      case 'chassis':
        navigate(`/admin/inventory/chassis/${id}`);
        break;
      default:
        break;
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
    setShowSearchResults(false);
  };

  return (
    <div className="top-menu bg-white border-b z-10">
      {/* Menu toggle and Search */}
      <div className="flex items-center h-full w-full">
        <button
          onClick={() => {
            // Prevent immediate closing of sidebar after toggling
            if (typeof toggleSidebar === 'function') {
              // Add a small delay to ensure the click is processed
              setTimeout(() => {
                toggleSidebar();
              }, 10);
            }
          }}
          className="text-gray-600 hover:text-indigo-700 mr-2 flex-shrink-0 p-2"
          aria-label="Toggle sidebar"
        >
          <Menu size={20} />
        </button>
        <div ref={searchRef} className="relative w-[35%] search-container flex-shrink-0" style={{ border: 'none' }}>
          <div className="relative flex items-center">
            <input
              type="text"
              placeholder="Search..."
              className="w-full pl-8 pr-8 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200 h-10"
              style={{
                border: '1px solid #e5e7eb',
                borderRadius: '0.375rem',
                boxShadow: 'none',
                background: 'white',
                position: 'relative',
                zIndex: 0
              }}
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={() => {
                if (searchQuery.length >= 2 && searchResults) {
                  setShowSearchResults(true);
                }
              }}
            />
            <Search className="absolute left-2.5 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 p-0"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Search Results Dropdown */}
          {showSearchResults && (
            <div className="absolute top-full left-0 w-96 mt-1 bg-white border rounded-md shadow-lg z-[1000] max-h-96 overflow-y-auto">
              {isSearching ? (
                <div className="p-4 text-center text-gray-500">
                  <div className="animate-spin inline-block w-4 h-4 border-2 border-gray-300 border-t-indigo-600 rounded-full mr-2"></div>
                  Searching...
                </div>
              ) : searchResults ? (
                <div>
                  {/* Invoices */}
                  {searchResults.invoices && searchResults.invoices.length > 0 && (
                    <div className="border-b">
                      <div className="px-3 py-2 bg-gray-50 text-xs font-semibold text-gray-500 uppercase">
                        Invoices
                      </div>
                      {searchResults.invoices.map((invoice) => (
                        <button
                          key={`invoice-${invoice.id}`}
                          className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-start"
                          onClick={() => navigateToResult('invoice', invoice.id)}
                        >
                          <FileText className="w-4 h-4 mt-0.5 mr-2 text-indigo-600 flex-shrink-0" />
                          <div>
                            <div className="font-medium text-sm">
                              {invoice.isProforma ? 'Proforma' : 'Invoice'} #{invoice.id}
                            </div>
                            <div className="text-xs text-gray-500">
                              {invoice.client_name} - {invoice.status} - €{invoice.total}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Clients */}
                  {searchResults.clients && searchResults.clients.length > 0 && (
                    <div className="border-b">
                      <div className="px-3 py-2 bg-gray-50 text-xs font-semibold text-gray-500 uppercase">
                        Clients
                      </div>
                      {searchResults.clients.map((client) => (
                        <button
                          key={`client-${client.id}`}
                          className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-start"
                          onClick={() => navigateToResult('client', client.id)}
                        >
                          <Users className="w-4 h-4 mt-0.5 mr-2 text-indigo-600 flex-shrink-0" />
                          <div>
                            <div className="font-medium text-sm">{client.name}</div>
                            <div className="text-xs text-gray-500">
                              {client.email} {client.location && `- ${client.location}`}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Servers */}
                  {searchResults.servers && searchResults.servers.length > 0 && (
                    <div className="border-b">
                      <div className="px-3 py-2 bg-gray-50 text-xs font-semibold text-gray-500 uppercase">
                        Servers
                      </div>
                      {searchResults.servers.map((server) => (
                        <button
                          key={`server-${server.id}`}
                          className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-start"
                          onClick={() => navigateToResult('server', server.id)}
                        >
                          <Server className="w-4 h-4 mt-0.5 mr-2 text-indigo-600 flex-shrink-0" />
                          <div>
                            <div className="font-medium text-sm">{server.label}</div>
                            <div className="text-xs text-gray-500">
                              {server.hostname} - {server.status} {server.location && `- ${server.location}`}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Switches */}
                  {searchResults.switches && searchResults.switches.length > 0 && (
                    <div className="border-b">
                      <div className="px-3 py-2 bg-gray-50 text-xs font-semibold text-gray-500 uppercase">
                        Switches
                      </div>
                      {searchResults.switches.map((switchItem) => (
                        <button
                          key={`switch-${switchItem.id}`}
                          className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-start"
                          onClick={() => navigateToResult('switch', switchItem.id)}
                        >
                          <Network className="w-4 h-4 mt-0.5 mr-2 text-indigo-600 flex-shrink-0" />
                          <div>
                            <div className="font-medium text-sm">{switchItem.label}</div>
                            <div className="text-xs text-gray-500">
                              {switchItem.hostname} - {switchItem.status} {switchItem.location && `- ${switchItem.location}`}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Blades */}
                  {searchResults.blades && searchResults.blades.length > 0 && (
                    <div className="border-b">
                      <div className="px-3 py-2 bg-gray-50 text-xs font-semibold text-gray-500 uppercase">
                        Blade Servers
                      </div>
                      {searchResults.blades.map((blade) => (
                        <button
                          key={`blade-${blade.id}`}
                          className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-start"
                          onClick={() => navigateToResult('blade', blade.id)}
                        >
                          <Layers className="w-4 h-4 mt-0.5 mr-2 text-indigo-600 flex-shrink-0" />
                          <div>
                            <div className="font-medium text-sm">{blade.label}</div>
                            <div className="text-xs text-gray-500">
                              {blade.hostname} - {blade.status} {blade.chassis && `- ${blade.chassis}`}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Chassis */}
                  {searchResults.chassis && searchResults.chassis.length > 0 && (
                    <div className="border-b">
                      <div className="px-3 py-2 bg-gray-50 text-xs font-semibold text-gray-500 uppercase">
                        Chassis
                      </div>
                      {searchResults.chassis.map((chassis) => (
                        <button
                          key={`chassis-${chassis.id}`}
                          className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-start"
                          onClick={() => navigateToResult('chassis', chassis.id)}
                        >
                          <Box className="w-4 h-4 mt-0.5 mr-2 text-indigo-600 flex-shrink-0" />
                          <div>
                            <div className="font-medium text-sm">{chassis.label}</div>
                            <div className="text-xs text-gray-500">
                              {chassis.status} - {chassis.rack} {chassis.location && `- ${chassis.location}`}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* No Results */}
                  {(!searchResults.invoices || searchResults.invoices.length === 0) &&
                   (!searchResults.clients || searchResults.clients.length === 0) &&
                   (!searchResults.servers || searchResults.servers.length === 0) &&
                   (!searchResults.switches || searchResults.switches.length === 0) &&
                   (!searchResults.blades || searchResults.blades.length === 0) &&
                   (!searchResults.chassis || searchResults.chassis.length === 0) && (
                    <div className="p-4 text-center text-gray-500">
                      No results found
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-4 text-center text-gray-500">
                  Type at least 2 characters to search
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Top Right Icons */}
      <div className="flex items-center space-x-4 h-full">

        {/* Ticket Notifications */}
        <div className="relative flex-shrink-0" ref={ticketNotificationsMenuRef}>
          <button
            onClick={toggleTicketNotifications}
            className={`flex items-center p-1 rounded-lg transition-colors ${
              ticketNotifications && (ticketNotifications.notification_stats?.total_unread || 0) > 0 ? 'text-red-600 hover:text-red-700' :
              'text-gray-600 hover:text-indigo-700'
            }`}
            aria-label="Ticket Notifications"
            title="Ticket Notifications"
          >
            <Bell className="w-5 h-5" />
            {ticketNotifications && (ticketNotifications.notification_stats?.total_unread || 0) > 0 && (
              <span className="absolute -top-1 -right-1 rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold bg-red-500 text-white">
                {ticketNotifications.notification_stats.total_unread}
              </span>
            )}
          </button>

          {showTicketNotifications && (
            <div className="absolute right-0 mt-2 w-96 bg-white border rounded-lg shadow-lg z-[1000] max-h-96 overflow-y-auto">
              {/* Header */}
              <div className="px-4 py-3 border-b bg-gray-50 rounded-t-lg">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900 flex items-center">
                    <Bell className="w-4 h-4 mr-2" />
                    Ticket Notifications
                  </h3>
                </div>
              </div>

              {ticketNotifications ? (
                <div>
                  {/* Statistics */}
                  <div className="px-4 py-3 border-b">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">New Tickets:</span>
                        <span className={`font-medium ${(ticketNotifications.notification_stats?.new_tickets || 0) > 0 ? 'text-red-600' : 'text-gray-900'}`}>
                          {ticketNotifications.notification_stats?.new_tickets || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Customer Replies:</span>
                        <span className={`font-medium ${(ticketNotifications.notification_stats?.customer_replies || 0) > 0 ? 'text-orange-600' : 'text-gray-900'}`}>
                          {ticketNotifications.notification_stats?.customer_replies || 0}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 text-xs text-gray-500">
                      Last updated: {new Date(ticketNotifications.last_updated).toLocaleTimeString()}
                    </div>
                  </div>

                  {/* New Tickets */}
                  {ticketNotifications.new_tickets && ticketNotifications.new_tickets.length > 0 && (
                    <div className="px-4 py-3 border-b">
                      <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                        <FileText className="w-4 h-4 mr-1" />
                        New Tickets
                      </h4>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {ticketNotifications.new_tickets.map((ticket) => (
                          <div key={ticket.id} className="text-xs border rounded p-2 bg-red-50 hover:bg-red-100 cursor-pointer"
                               onClick={() => navigate(`/admin/tickets/${ticket.id}`)}>
                            <div className="flex items-center justify-between mb-1">
                              <span className="font-medium">#{ticket.id} - {ticket.subject}</span>
                              <span className={`px-1 py-0.5 rounded text-xs font-medium ${
                                ticket.priority === 'High' ? 'bg-red-100 text-red-800' :
                                ticket.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {ticket.priority}
                              </span>
                            </div>
                            <div className="text-gray-600">
                              <div className="flex items-center">
                                <User className="w-3 h-3 mr-1" />
                                {ticket.customer_name}
                              </div>
                              <div className="mt-1">
                                Created: {ticket.time_ago}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Customer Replies */}
                  {ticketNotifications.customer_replies && ticketNotifications.customer_replies.length > 0 && (
                    <div className="px-4 py-3">
                      <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                        <Users className="w-4 h-4 mr-1" />
                        Customer Replies
                      </h4>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {ticketNotifications.customer_replies.map((reply) => (
                          <div key={reply.ticket_id} className="text-xs border rounded p-2 bg-orange-50 hover:bg-orange-100 cursor-pointer"
                               onClick={() => navigate(`/admin/tickets/${reply.ticket_id}`)}>
                            <div className="flex items-center justify-between mb-1">
                              <span className="font-medium">#{reply.ticket_id} - {reply.subject}</span>
                            </div>
                            <div className="text-gray-600">
                              <div className="flex items-center">
                                <User className="w-3 h-3 mr-1" />
                                {reply.customer_name}
                              </div>
                              <div className="mt-1 italic">
                                "{reply.last_message}"
                              </div>
                              <div className="mt-1">
                                Replied: {reply.time_ago}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* No Notifications */}
                  {(!ticketNotifications.new_tickets || ticketNotifications.new_tickets.length === 0) &&
                   (!ticketNotifications.customer_replies || ticketNotifications.customer_replies.length === 0) && (
                    <div className="px-4 py-6 text-center text-gray-500">
                      <Bell className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                      <p>No new notifications</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="px-4 py-6 text-center text-gray-500">
                  <div className="animate-spin inline-block h-6 w-6 border-2 border-gray-300 border-t-indigo-600 rounded-full mb-2"></div>
                  <p>Loading notifications...</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Switch Queue Monitor */}
        <div className="relative flex-shrink-0" ref={queueMenuRef}>
          <button
            onClick={toggleQueueMonitor}
            className={`flex items-center p-1 rounded-lg transition-colors ${
              queueData?.queue_health === 'processing' ? 'text-blue-600 hover:text-blue-700' :
              queueData?.queue_health === 'queued' ? 'text-orange-600 hover:text-orange-700' :
              queueData?.queue_health === 'warning' ? 'text-red-600 hover:text-red-700' :
              'text-gray-600 hover:text-indigo-700'
            }`}
            aria-label="Switch Queue Monitor"
            title="Switch Queue Monitor"
          >
            <Activity className="w-5 h-5" />
            {queueData && ((queueData.queue_stats?.queued || 0) > 0 || (queueData.queue_stats?.processing || 0) > 0) && (
              <span className={`absolute -top-1 -right-1 rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold ${
                queueData.queue_health === 'processing' ? 'bg-blue-500 text-white' :
                queueData.queue_health === 'queued' ? 'bg-orange-500 text-white' :
                'bg-gray-500 text-white'
              }`}>
                {parseInt(queueData.queue_stats?.queued || 0) + parseInt(queueData.queue_stats?.processing || 0)}
              </span>
            )}
          </button>

          {showQueueMonitor && (
            <div className="absolute right-0 mt-2 w-80 bg-white border rounded-lg shadow-lg z-[1000] max-h-96 overflow-y-auto">
              {/* Header */}
              <div className="px-4 py-3 border-b bg-gray-50 rounded-t-lg">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900 flex items-center">
                    <Activity className="w-4 h-4 mr-2" />
                    Switch Queue Monitor
                  </h3>
                                     <div className="flex items-center space-x-2">
                     <button
                       onClick={restartQueue}
                       className="text-xs bg-indigo-600 text-white px-2 py-1 rounded hover:bg-indigo-700 flex items-center disabled:opacity-50"
                       title="Restart Queue"
                       disabled={restartLoading}
                     >
                       <RefreshCw className={`w-3 h-3 mr-1 ${restartLoading ? 'animate-spin' : ''}`} />
                       Restart
                     </button>
                    
                   </div>
                </div>
              </div>

              {queueData ? (
                <div>
                  {/* Statistics */}
                  <div className="px-4 py-3 border-b">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Queued:</span>
                        <span className={`font-medium ${(queueData.queue_stats?.queued || 0) > 0 ? 'text-orange-600' : 'text-gray-900'}`}>
                          {queueData.queue_stats?.queued || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Processing:</span>
                        <span className={`font-medium ${(queueData.queue_stats?.processing || 0) > 0 ? 'text-blue-600' : 'text-gray-900'}`}>
                          {queueData.queue_stats?.processing || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Completed:</span>
                        <span className="font-medium text-green-600">{queueData.queue_stats?.completed || 0}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Failed:</span>
                        <span className={`font-medium ${(queueData.queue_stats?.failed || 0) > 0 ? 'text-red-600' : 'text-gray-900'}`}>
                          {queueData.queue_stats?.failed || 0}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 text-xs text-gray-500">
                      Active locks: {queueData.active_locks || 0} | Last updated: {new Date(queueData.last_updated).toLocaleTimeString()}
                    </div>
                  </div>

                  {/* Recent Operations */}
                  <div className="px-4 py-3">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Operations</h4>
                    {queueData.recent_operations && queueData.recent_operations.length > 0 ? (
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {queueData.recent_operations.map((op) => (
                          <div key={op.id} className="text-xs border rounded p-2 bg-gray-50">
                            <div className="flex items-center justify-between mb-1">
                              <span className="font-medium">{op.operation_type.charAt(0).toUpperCase() + op.operation_type.slice(1)}</span>
                              <div className="flex items-center space-x-2">
                                <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                  op.status === 'completed' ? 'bg-green-100 text-green-800' :
                                  op.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                                  op.status === 'failed' ? 'bg-red-100 text-red-800' :
                                  'bg-orange-100 text-orange-800'
                                }`}>
                                  {op.status === 'completed' ? <CheckCircle className="w-3 h-3 inline mr-1" /> :
                                   op.status === 'processing' ? <Play className="w-3 h-3 inline mr-1" /> :
                                   op.status === 'failed' ? <AlertCircle className="w-3 h-3 inline mr-1" /> :
                                   <Pause className="w-3 h-3 inline mr-1" />}
                                  {op.status.charAt(0).toUpperCase() + op.status.slice(1)}
                                </span>
                                {op.status === 'failed' && (
                                  <button
                                    onClick={() => retryTask(op.id)}
                                    disabled={retryingTasks.has(op.id)}
                                    className="text-xs bg-red-600 text-white px-2 py-0.5 rounded hover:bg-red-700 flex items-center disabled:opacity-50"
                                    title="Retry Task"
                                  >
                                    <RefreshCw className={`w-3 h-3 mr-1 ${retryingTasks.has(op.id) ? 'animate-spin' : ''}`} />
                                    {retryingTasks.has(op.id) ? 'Retrying' : 'Retry'}
                                  </button>
                                )}
                              </div>
                            </div>
                            <div className="text-gray-600">
                              <div className="flex items-center">
                                <Network className="w-3 h-3 mr-1" />
                                {getSwitchName(op.switch_ip)}:{op.port}
                              </div>
                              {op.server_label && (
                                <div className="flex items-center mt-1">
                                  <Server className="w-3 h-3 mr-1" />
                                  {op.server_label}
                                </div>
                              )}
                              <div className="mt-1">
                                Created: {new Date(op.created_at).toLocaleTimeString()}
                              </div>
                              {op.error_message && (
                                <div className="mt-1 text-red-600">
                                  Error: {op.error_message}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">No recent operations</p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="px-4 py-6 text-center text-gray-500">
                  <div className="animate-spin inline-block h-6 w-6 border-2 border-gray-300 border-t-indigo-600 rounded-full mb-2"></div>
                  <p>Loading queue data...</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Server Installation Monitor */}
        <div className="relative flex-shrink-0" ref={pxeMenuRef}>
          <button
            onClick={togglePXEMonitor}
            className={`flex items-center p-1 rounded-lg transition-colors ${
              pxeData?.installation_health === 'active' ? 'text-blue-600 hover:text-blue-700' :
              pxeData?.installation_health === 'busy' ? 'text-orange-600 hover:text-orange-700' :
              pxeData?.installation_health === 'error' ? 'text-red-600 hover:text-red-700' :
              'text-gray-600 hover:text-indigo-700'
            }`}
            aria-label="Server Installation Monitor"
            title="Server Installation Monitor"
          >
            <HardDrive className="w-5 h-5" />
            {pxeData && (pxeData.installation_stats.pending > 0 || pxeData.installation_stats.active > 0) && (
              <span className={`absolute -top-1 -right-1 rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold ${
                pxeData.installation_health === 'active' ? 'bg-blue-500 text-white' :
                pxeData.installation_health === 'busy' ? 'bg-orange-500 text-white' :
                pxeData.installation_health === 'error' ? 'bg-red-500 text-white' :
                'bg-gray-500 text-white'
              }`}>
                {parseInt(pxeData.installation_stats.pending) + parseInt(pxeData.installation_stats.active)}
              </span>
            )}
          </button>

          {showPXEMonitor && (
            <div className="absolute right-0 mt-2 w-80 bg-white border rounded-lg shadow-lg z-[1000] max-h-96 overflow-y-auto">
              {/* Header */}
              <div className="px-4 py-3 border-b bg-gray-50 rounded-t-lg">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900 flex items-center">
                    <HardDrive className="w-4 h-4 mr-2" />
                    Server Installation Monitor
                  </h3>
                </div>
              </div>

              {pxeData ? (
                <div>
                  {/* Statistics */}
                  <div className="px-4 py-3 border-b">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Pending:</span>
                        <span className={`font-medium ${pxeData.installation_stats.pending > 0 ? 'text-orange-600' : 'text-gray-900'}`}>
                          {pxeData.installation_stats.pending}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Installing:</span>
                        <span className={`font-medium ${pxeData.installation_stats.active > 0 ? 'text-blue-600' : 'text-gray-900'}`}>
                          {pxeData.installation_stats.active}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Completed:</span>
                        <span className="font-medium text-green-600">{pxeData.installation_stats.completed}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Failed:</span>
                        <span className={`font-medium ${pxeData.installation_stats.failed > 0 ? 'text-red-600' : 'text-gray-900'}`}>
                          {pxeData.installation_stats.failed}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 text-xs text-gray-500">
                      Last updated: {new Date(pxeData.last_updated).toLocaleTimeString()}
                    </div>
                  </div>

                  {/* Recent Installations */}
                  <div className="px-4 py-3">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Installations</h4>
                    {pxeData.recent_installations && pxeData.recent_installations.length > 0 ? (
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {pxeData.recent_installations.map((installation) => (
                          <div key={installation.id} className="text-xs border rounded p-2 bg-gray-50">
                            <div className="flex items-center justify-between mb-1">
                              <span className="font-medium">{installation.server_label}</span>
                              <div className="flex items-center space-x-2">
                                <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                  installation.status === 'completed' ? 'bg-green-100 text-green-800' :
                                  installation.status === 'active' ? 'bg-blue-100 text-blue-800' :
                                  installation.status === 'failed' ? 'bg-red-100 text-red-800' :
                                  installation.status === 'cancelled' ? 'bg-gray-100 text-gray-800' :
                                  'bg-orange-100 text-orange-800'
                                }`}>
                                  {installation.status === 'completed' ? <CheckCircle className="w-3 h-3 inline mr-1" /> :
                                   installation.status === 'active' ? <Download className="w-3 h-3 inline mr-1" /> :
                                   installation.status === 'failed' ? <AlertCircle className="w-3 h-3 inline mr-1" /> :
                                   installation.status === 'cancelled' ? <XCircle className="w-3 h-3 inline mr-1" /> :
                                   <Clock className="w-3 h-3 inline mr-1" />}
                                  {installation.status.charAt(0).toUpperCase() + installation.status.slice(1)}
                                </span>
                                {installation.status === 'failed' && (
                                  <button
                                    onClick={() => retryPXESession(installation.id)}
                                    disabled={retryingSessions.has(installation.id)}
                                    className="text-xs bg-red-600 text-white px-2 py-0.5 rounded hover:bg-red-700 flex items-center disabled:opacity-50"
                                    title="Retry Installation"
                                  >
                                    <RefreshCw className={`w-3 h-3 mr-1 ${retryingSessions.has(installation.id) ? 'animate-spin' : ''}`} />
                                    {retryingSessions.has(installation.id) ? 'Retrying' : 'Retry'}
                                  </button>
                                )}
                                {(installation.status === 'pending' || installation.status === 'active') && (
                                  <button
                                    onClick={() => cancelPXESession(installation.id)}
                                    disabled={cancellingSessions.has(installation.id)}
                                    className="text-xs bg-gray-600 text-white px-2 py-0.5 rounded hover:bg-gray-700 flex items-center disabled:opacity-50"
                                    title="Cancel Installation"
                                  >
                                    <XCircle className={`w-3 h-3 mr-1 ${cancellingSessions.has(installation.id) ? 'animate-spin' : ''}`} />
                                    {cancellingSessions.has(installation.id) ? 'Cancelling' : 'Cancel'}
                                  </button>
                                )}
                              </div>
                            </div>
                            <div className="text-gray-600">
                              <div className="flex items-center">
                                <Server className="w-3 h-3 mr-1" />
                                {installation.server_type} #{installation.server_id}
                              </div>
                              <div className="flex items-center mt-1">
                                <Download className="w-3 h-3 mr-1" />
                                {installation.os_template}
                              </div>
                              <div className="mt-1">
                                Started: {new Date(installation.started_at).toLocaleTimeString()}
                              </div>
                              {installation.completed_at && (
                                <div className="mt-1">
                                  Completed: {new Date(installation.completed_at).toLocaleTimeString()}
                                </div>
                              )}
                              {installation.error_message && (
                                <div className="mt-1 text-red-600">
                                  Error: {installation.error_message}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">No recent installations</p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="px-4 py-6 text-center text-gray-500">
                  <div className="animate-spin inline-block h-6 w-6 border-2 border-gray-300 border-t-indigo-600 rounded-full mb-2"></div>
                  <p>Loading installation data...</p>
                </div>
              )}
            </div>
          )}
        </div>
        
       
        <button
          onClick={() => navigate('/admin/email-settings')}
          className="flex items-center text-gray-600 hover:text-indigo-700"
          aria-label="Email Settings"
        >
          <Settings className="w-5 h-5 flex-shrink-0" />
        </button>

        {/* User profile dropdown */}
        <div className="relative flex-shrink-0" ref={userMenuRef}>
          <button
            onClick={toggleUserMenu}
            className="flex items-center text-gray-600 hover:text-indigo-700"
            aria-label="User menu"
          >
            <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700">
              <User className="w-4 h-4" />
            </div>
            <ChevronDown className="w-4 h-4 ml-1 hidden sm:block" />
          </button>

          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white border rounded-lg shadow-lg z-[1000]">
              <button
                onClick={navigateToProfile}
                className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center space-x-2"
              >
                <User className="w-4 h-4" />
                <span>My Profile</span>
              </button>
              <button
                onClick={handleLogout}
                className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600 flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Sign Out</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/*
        Invoice Modal - Using ReactDOM.createPortal would be better for modals to ensure they're
        rendered at the root level of the DOM and avoid z-index issues with the sidebar.
        For now, we're using inline styles with very high z-index values as a workaround.
      */}
      {invoiceModalOpen && selectedInvoice && (
        <div
          onClick={(e) => {
            // Only close if clicking directly on the backdrop, not on the modal content
            if (e.target === e.currentTarget) {
              closeInvoiceModal();
            }
          }}
          style={{
            zIndex: 999999,
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backdropFilter: 'blur(2px)', // Add slight blur effect
            WebkitBackdropFilter: 'blur(2px)', // For Safari support
          }}
          className="fixed inset-0 flex items-center justify-center">
          <div style={{zIndex: 999999}} className="bg-white rounded-lg shadow-lg max-w-4xl w-[95%] mx-auto max-h-[90vh] overflow-y-auto">
            {/* Custom header with close button */}
            <div className="p-6 border-b flex justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  {selectedInvoice.isProforma ? 'Proforma Invoice' : 'Invoice'} #{selectedInvoice.id}
                </h2>
                <div className="flex flex-wrap gap-2">
                  {/* Type badge */}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${selectedInvoice.isProforma ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`}>
                    <FileText className="w-4 h-4 mr-1" />
                    {selectedInvoice.isProforma ? 'Proforma' : 'Invoice'}
                  </span>

                  {/* Invoice number badge */}
                  <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-gray-100 text-gray-800">
                    <FileText className="w-4 h-4 mr-1" />
                    #{selectedInvoice.id}
                  </span>

                  {/* Status badge */}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit
                    ${selectedInvoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                      selectedInvoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                      selectedInvoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'}`}>
                    {selectedInvoice.status === 'Paid' ? (
                      <CheckCircle className="w-4 h-4 mr-1" />
                    ) : selectedInvoice.status === 'Overdue' ? (
                      <AlertCircle className="w-4 h-4 mr-1" />
                    ) : (
                      <Clock className="w-4 h-4 mr-1" />
                    )}
                    {selectedInvoice.status}
                  </span>
                </div>
              </div>
              <button
                onClick={closeInvoiceModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            {/* InvoiceView component */}
            <InvoiceView
              invoice={selectedInvoice}
              hideHeader={true}
              className="border-t-0 rounded-t-none w-full"
              onPrint={() => console.log('Print invoice:', selectedInvoice.id)}
              onDownload={() => console.log('Download invoice:', selectedInvoice.id)}
              defaultActiveTab="details"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TopMenu;